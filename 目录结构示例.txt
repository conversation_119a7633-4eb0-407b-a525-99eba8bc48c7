RRU计算器 - 目录结构示例
===============================================================================

正确的目录结构应该如下所示：

RRU计算器/
├── rru_calculator.exe                    ← 主程序（必需）
├── 运行RRU计算器.bat                     ← 一键运行脚本（推荐使用）
├── README_EXE使用说明.txt                ← 详细使用说明
├── 快速入门指南.txt                      ← 快速入门指南
├── 目录结构示例.txt                      ← 本文件
│
├── 45G存量数据/                          ← 存量板数据目录（必需）
│   ├── 存量_板_20250529_171740.csv      ← 存量板文件1
│   ├── 存量_板_20250529_172104.csv      ← 存量板文件2
│   ├── 存量_板_20250529_172153.csv      ← 存量板文件3
│   ├── 存量_板_20250529_172316.csv      ← 存量板文件4
│   ├── 存量_板_20250529_172434.csv      ← 存量板文件5
│   ├── 存量_板_20250529_172542.csv      ← 存量板文件6
│   ├── 存量_板_20250529_172650.csv      ← 存量板文件7
│   └── 子目录/                           ← 支持子目录
│       └── 更多存量板文件.csv
│
├── 工参数据/                             ← 工程参数目录（必需）
│   ├── 4G工参_update_20250523.csv       ← 4G工参（文件名必须含"4G"）
│   └── 5G工参_update_20250523.csv       ← 5G工参（文件名必须含"5G"）
│
└── 输出结果/                             ← 程序运行后自动生成
    ├── RRU计算结果_20250530_143025.xlsx ← 生成的分析报告1
    ├── RRU计算结果_20250530_151230.xlsx ← 生成的分析报告2
    └── RRU计算结果_20250530_164515.xlsx ← 生成的分析报告3

===============================================================================

📁 目录说明：

【45G存量数据/】
• 作用：存放基站硬件设备清单
• 文件格式：CSV文件
• 文件数量：支持多个文件
• 子目录：支持任意层级的子目录
• 编码：支持GBK和UTF-8

【工参数据/】
• 作用：存放4G和5G工程参数
• 4G工参：文件名必须包含"4G"关键字
• 5G工参：文件名必须包含"5G"关键字
• 最少要求：至少需要4G或5G工参文件中的一个

📋 文件命名规则：

【存量板文件】
✓ 存量_板_20250529_171740.csv
✓ 存量板数据_2025年5月.csv
✓ stock_data_20250529.csv
✓ 任意名称.csv（只要是CSV格式）

【4G工参文件】
✓ 4G工参_update_20250523.csv
✓ LTE_4G_参数_20250523.csv
✓ 工参_4G_20250523.csv
✗ 工参_LTE_20250523.csv（不包含"4G"）

【5G工参文件】
✓ 5G工参_update_20250523.csv
✓ NR_5G_参数_20250523.csv
✓ 工参_5G_20250523.csv
✗ 工参_NR_20250523.csv（不包含"5G"）

⚠️ 常见错误：

【错误1】目录不存在
问题：没有创建45G存量数据或工参数据目录
解决：程序会自动创建，或手动创建这两个目录

【错误2】文件命名错误
问题：4G工参文件名不包含"4G"，5G工参文件名不包含"5G"
解决：重命名文件，确保包含正确的关键字

【错误3】文件格式错误
问题：文件不是CSV格式或编码不正确
解决：确保文件为CSV格式，编码为GBK或UTF-8

【错误4】字段缺失
问题：CSV文件缺少必需的字段
解决：检查并补充必需字段

===============================================================================

💡 使用建议：

1. 首次使用时，运行程序会自动创建必需的目录
2. 将数据文件按类型放入对应目录
3. 启动程序，会有详细的提示信息
4. 生成的Excel报告文件名包含时间戳，便于版本管理
5. 支持批量处理多个存量板文件，提高处理效率

===============================================================================
