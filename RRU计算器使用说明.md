# RRU计算器 - 使用说明

## 📋 程序概述

RRU计算器是一个高性能的射频单元数据处理与分析工具，用于处理4G/5G基站的存量板数据和工参数据，生成详细的RRU统计分析报告。

## 📁 目录结构说明

### 必需目录

程序运行需要以下两个目录，如果不存在会自动创建：

#### 1. `45G存量数据/` 目录
**作用**: 存放基站存量板数据文件
**文件格式**: CSV文件（支持GBK编码）
**文件内容**: 基站硬件设备清单数据

**必需字段**:
- 网元名称
- 单板名称  
- 单板类型
- 机框号
- 机柜号
- 资产序列号
- 槽位号

**支持的单板类型**:
- MRRU (多模射频单元)
- MPMU (多模功放单元)
- MPRF (多模射频单元)
- AIRU (有源室内射频单元)
- LRRU (LTE射频单元)
- EPRRU (增强型射频单元)

**目录结构示例**:
```
45G存量数据/
├── 存量_板_20250529_171740.csv
├── 存量_板_20250529_172104.csv
├── 子目录1/
│   └── 存量_板_20250529_172153.csv
└── 子目录2/
    └── 存量_板_20250529_172316.csv
```

#### 2. `工参数据/` 目录
**作用**: 存放4G和5G工程参数文件
**文件格式**: CSV文件（支持GBK编码）
**文件内容**: 基站小区配置和RRU绑定信息

**4G工参文件**:
- 文件名必须包含 "4G" 关键字
- 示例: `4G工参_update_20250523.csv`

**必需字段**:
- ECGI (小区全局标识)
- 站名(网管拓扑)
- 站号(eNodeB标识)
- 站名(eNodeB名称)
- 下行带宽
- 小区级射频单元(RRU)数量
- 小区级射频单元信息序列
- 小区级RRU型号数量
- 小区级RRU型号(去重)
- 基站级基带板(BBU)数量
- 基站级基带板信息
- 入网时间

**5G工参文件**:
- 文件名必须包含 "5G" 关键字
- 示例: `5G工参_update_20250523.csv`

**必需字段**:
- ECGI (小区全局标识)
- NAME (小区名称)
- 站号(gNodeB标识)
- 站名(gNodeB功能名称)
- 下行带宽
- 小区射频单元(RRU)数量
- 小区级射频单元信息序列
- 小区级RRU型号数量
- 小区级RRU型号序列
- 基站级基带板(BBU)数量
- 基站级基带板信息
- 入网时间

**目录结构示例**:
```
工参数据/
├── 4G工参_update_20250523.csv
└── 5G工参_update_20250523.csv
```

## 🚀 使用步骤

### 1. 准备数据文件
1. 将存量板CSV文件放入 `45G存量数据/` 目录
2. 将4G工参CSV文件放入 `工参数据/` 目录（文件名包含"4G"）
3. 将5G工参CSV文件放入 `工参数据/` 目录（文件名包含"5G"）

### 2. 运行程序
- **方法1**: 双击 `运行RRU计算器.bat`
- **方法2**: 直接运行 `rru_calculator.exe`

### 3. 查看结果
程序运行完成后会生成：
- `RRU计算结果_YYYYMMDD_HHMMSS.xlsx` - 详细分析报告

## ⚠️ 文件缺失处理

### 存量板数据缺失
**现象**: 
```
[ERROR] No CSV files found in stock data directory
Please place stock CSV files in the 45G存量数据 directory
```

**解决方案**:
1. 检查 `45G存量数据/` 目录是否存在
2. 确认目录中有CSV格式的存量板文件
3. 验证CSV文件包含必需的字段
4. 检查文件编码（支持GBK和UTF-8）

### 4G工参缺失
**现象**: 
```
▶ 未找到4G工参文件
├─ 未找到4G工参，仅计算5G小区级RRU
```

**影响**: 
- 无法统计4G小区级RRU数量
- 4G相关字段显示为0或空值
- 仍可正常处理5G数据

**解决方案**:
1. 在 `工参数据/` 目录中放入包含"4G"的CSV文件
2. 确认文件包含必需的4G工参字段

### 5G工参缺失
**现象**: 
```
▶ 未找到5G工参文件
├─ 未找到5G工参，仅计算4G小区级RRU
```

**影响**: 
- 无法统计5G小区级RRU数量
- 5G相关字段显示为0或空值
- 100M/60M开通状态显示为"否"
- 仍可正常处理4G数据

**解决方案**:
1. 在 `工参数据/` 目录中放入包含"5G"的CSV文件
2. 确认文件包含必需的5G工参字段

### 工参完全缺失
**现象**: 
```
>>> 数据处理失败 <<<
工参文件不存在
```

**影响**: 
- 程序无法继续执行
- 不会生成Excel报告

**解决方案**:
1. 至少提供4G或5G工参文件中的一个
2. 确认文件名包含"4G"或"5G"关键字
3. 验证文件格式和字段完整性

## 📊 输出报告说明

### Excel文件内容
生成的Excel报告包含以下信息：

**基础信息**:
- 网元名称、单板名称、单板类型
- 机框号、机柜号、资产序列号、槽位号

**合并统计**:
- 站号、下行带宽、入网时间
- 基站级基带板(BBU)相关信息

**4G统计**:
- 4G小区级射频单元(RRU)数量
- 4G小区级RRU型号信息
- 4G绑定小区数量和列表

**5G统计**:
- 5G小区级射频单元(RRU)数量
- 5G小区级RRU型号信息
- 5G绑定小区数量和列表
- 100M/60M开通状态

### 文件命名规则
- 格式: `RRU计算结果_YYYYMMDD_HHMMSS.xlsx`
- 示例: `RRU计算结果_20250530_143025.xlsx`
- 时间戳精确到秒，避免文件名冲突

## 🔧 故障排除

### 常见问题

1. **程序无法启动**
   - 检查是否安装了必要的运行库
   - 确认exe文件完整性

2. **CSV文件读取失败**
   - 检查文件编码（推荐GBK）
   - 验证CSV格式正确性
   - 确认必需字段存在

3. **Excel生成失败**
   - 检查磁盘空间是否充足
   - 确认没有同名文件被其他程序占用
   - 验证输出目录写入权限

4. **数据统计异常**
   - 检查工参文件字段完整性
   - 验证数据格式正确性
   - 确认站名匹配规则

### 性能建议

- **大数据集**: 建议使用SSD硬盘提升处理速度
- **内存要求**: 推荐8GB以上内存处理大量数据
- **文件组织**: 合理组织CSV文件，避免单个文件过大

## 📞 技术支持

如遇到问题，请检查：
1. 数据文件格式和内容
2. 目录结构是否正确
3. 程序日志输出中的错误信息
4. 系统环境和权限设置

程序会输出详细的处理日志，包括文件读取状态、数据统计信息和错误提示，请根据日志信息进行问题排查。
