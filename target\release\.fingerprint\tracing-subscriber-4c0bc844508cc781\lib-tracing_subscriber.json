{"rustc": 16591470773350601817, "features": "[\"alloc\", \"ansi\", \"default\", \"env-filter\", \"fmt\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"regex\", \"registry\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 4817557058868189149, "profile": 10369491684090452477, "path": 7997839937313752072, "deps": [[1009387600818341822, "matchers", false, 5637819183674342511], [1017461770342116999, "sharded_slab", false, 14481908345359478708], [3722963349756955755, "once_cell", false, 6329152524538668337], [6048213226671835012, "smallvec", false, 3388290112942797651], [8606274917505247608, "tracing", false, 9020662520371239283], [8614575489689151157, "nu_ansi_term", false, 8365136445266682599], [9451456094439810778, "regex", false, 9046684583942019352], [10806489435541507125, "tracing_log", false, 1849638546823384760], [11033263105862272874, "tracing_core", false, 241022583916828150], [12409575957772518135, "time", false, 14014794582196887386], [12427285511609802057, "thread_local", false, 6179785243268075255]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tracing-subscriber-4c0bc844508cc781\\dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}