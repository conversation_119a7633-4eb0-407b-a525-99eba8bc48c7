===============================================================================
                            RRU计算器 - 快速入门指南
===============================================================================

📋 程序功能
-----------
处理基站存量板数据和4G/5G工参数据，生成RRU统计分析报告

📁 数据文件准备
--------------
1. 创建两个文件夹（程序会自动创建）：
   ├── 45G存量数据/     ← 放置存量板CSV文件
   └── 工参数据/        ← 放置4G和5G工参CSV文件

2. 存量板数据文件：
   - 文件格式：CSV（支持GBK编码）
   - 放置位置：45G存量数据/ 目录（支持子目录）
   - 必需字段：网元名称、单板名称、单板类型、机框号、机柜号、资产序列号、槽位号
   - 支持单板：MRRU、MPMU、MPRF、AIRU、LRRU、EPRRU

3. 4G工参文件：
   - 文件格式：CSV（支持GBK编码）
   - 文件名要求：必须包含"4G"关键字
   - 放置位置：工参数据/ 目录
   - 示例文件名：4G工参_update_20250523.csv

4. 5G工参文件：
   - 文件格式：CSV（支持GBK编码）
   - 文件名要求：必须包含"5G"关键字
   - 放置位置：工参数据/ 目录
   - 示例文件名：5G工参_update_20250523.csv

🚀 运行程序
-----------
方法1：双击 "运行RRU计算器.bat"
方法2：直接运行 "rru_calculator.exe"

📊 输出结果
-----------
生成文件：RRU计算结果_YYYYMMDD_HHMMSS.xlsx
包含内容：存量板信息 + 4G/5G RRU统计 + 小区绑定信息

⚠️ 常见错误及解决方案
--------------------

错误1：找不到存量板数据
现象：[ERROR] No CSV files found in stock data directory
解决：在 45G存量数据/ 目录中放入存量板CSV文件

错误2：未找到4G工参
现象：▶ 未找到4G工参文件
影响：4G相关统计为0，但程序继续运行
解决：在 工参数据/ 目录中放入包含"4G"的CSV文件

错误3：未找到5G工参
现象：▶ 未找到5G工参文件
影响：5G相关统计为0，100M/60M显示"否"，但程序继续运行
解决：在 工参数据/ 目录中放入包含"5G"的CSV文件

错误4：工参完全缺失
现象：>>> 数据处理失败 <<< 工参文件不存在
影响：程序终止，不生成报告
解决：至少提供4G或5G工参文件中的一个

📈 处理进度显示
--------------
程序运行时会显示：
- 数据读取进度
- Excel生成进度条（包含百分比和耗时）
- 最终统计汇总

🔧 性能建议
-----------
- 推荐使用SSD硬盘
- 建议8GB以上内存
- 确保足够的磁盘空间

📞 问题排查
-----------
1. 检查CSV文件格式和编码
2. 确认必需字段存在
3. 查看程序日志输出
4. 验证文件名包含正确关键字

===============================================================================
注意：程序支持处理大量数据，515,090条记录约需20秒处理时间
===============================================================================
