===============================================================================
                              RRU计算器 v1.0.0
                        射频单元数据处理与分析工具
===============================================================================

🎯 程序简介
-----------
RRU计算器是一个高性能的基站数据分析工具，专门用于处理4G/5G基站的存量板数据
和工程参数，自动生成详细的RRU（射频单元）统计分析报告。

📁 目录结构要求
--------------
程序运行前，请确保以下目录结构：

项目根目录/
├── rru_calculator.exe          ← 主程序
├── 45G存量数据/                ← 存量板数据目录
│   ├── 存量_板_文件1.csv
│   ├── 存量_板_文件2.csv
│   └── 子目录/
│       └── 更多存量板文件.csv
└── 工参数据/                   ← 工程参数目录
    ├── 4G工参_xxxx.csv         ← 4G工参（文件名必须含"4G"）
    └── 5G工参_xxxx.csv         ← 5G工参（文件名必须含"5G"）

📋 数据文件说明
--------------

【存量板数据】- 45G存量数据/ 目录
• 文件格式：CSV文件（支持GBK/UTF-8编码）
• 文件数量：支持多个文件，支持子目录
• 必需字段：
  - 网元名称（基站名称）
  - 单板名称（设备名称）
  - 单板类型（MRRU/MPMU/MPRF/AIRU/LRRU/EPRRU）
  - 机框号、机柜号、资产序列号、槽位号

【4G工程参数】- 工参数据/ 目录
• 文件格式：CSV文件（支持GBK/UTF-8编码）
• 文件命名：文件名必须包含"4G"关键字
• 必需字段：
  - ECGI（小区标识）
  - 站名(网管拓扑)
  - 站号(eNodeB标识)
  - 下行带宽
  - 小区级射频单元(RRU)数量
  - 小区级RRU型号信息
  - 基站级基带板(BBU)信息
  - 入网时间

【5G工程参数】- 工参数据/ 目录
• 文件格式：CSV文件（支持GBK/UTF-8编码）
• 文件命名：文件名必须包含"5G"关键字
• 必需字段：
  - ECGI（小区标识）
  - NAME（小区名称）
  - 站号(gNodeB标识)
  - 下行带宽
  - 小区射频单元(RRU)数量
  - 小区级RRU型号信息
  - 基站级基带板(BBU)信息
  - 入网时间

🚀 运行方法
-----------
【推荐方法】双击运行 "运行RRU计算器.bat"
【直接运行】双击 "rru_calculator.exe"

程序会自动：
1. 检查数据目录和文件
2. 读取并处理所有数据
3. 生成Excel分析报告
4. 显示处理进度和统计信息

📊 输出结果
-----------
• 文件名：RRU计算结果_YYYYMMDD_HHMMSS.xlsx
• 内容包含：
  - 基础设备信息（存量板数据）
  - 4G小区级RRU统计
  - 5G小区级RRU统计
  - 小区绑定信息
  - 100M/60M开通状态
  - 基站级BBU信息

⚠️ 错误处理指南
---------------

【错误1】存量板数据缺失
显示：[ERROR] No CSV files found in stock data directory
原因：45G存量数据/ 目录中没有CSV文件
解决：将存量板CSV文件放入该目录

【错误2】4G工参缺失
显示：▶ 未找到4G工参文件
影响：4G相关统计显示为0，程序继续运行
解决：在工参数据/目录中放入包含"4G"的CSV文件

【错误3】5G工参缺失
显示：▶ 未找到5G工参文件
影响：5G相关统计显示为0，100M/60M显示"否"
解决：在工参数据/目录中放入包含"5G"的CSV文件

【错误4】工参完全缺失
显示：>>> 数据处理失败 <<< 工参文件不存在
影响：程序终止，不生成报告
解决：至少提供4G或5G工参文件中的一个

【错误5】CSV文件格式错误
显示：CSV解析错误
原因：文件编码或格式问题
解决：确保CSV文件为GBK或UTF-8编码，字段完整

🔧 性能说明
-----------
• 处理速度：约25,000行/秒
• 内存需求：建议8GB以上
• 磁盘空间：确保有足够空间存储结果文件
• 测试数据：515,090条记录约需20秒

📈 进度显示
-----------
程序运行时会显示：
• 彩色日志输出
• 文件读取进度
• Excel生成进度条（百分比+耗时）
• 最终统计汇总表格

💡 使用技巧
-----------
1. 数据准备：确保CSV文件字段完整，编码正确
2. 文件命名：4G/5G工参文件名必须包含对应关键字
3. 目录组织：存量板数据支持子目录，便于分类管理
4. 结果查看：Excel文件包含多种统计维度，便于分析

📞 故障排除
-----------
如遇问题，请按以下步骤检查：
1. 确认目录结构正确
2. 检查CSV文件格式和编码
3. 验证必需字段存在
4. 查看程序日志输出
5. 确认文件名包含正确关键字

===============================================================================
版本信息：v1.0.0
技术支持：请保留完整的程序日志输出以便问题排查
===============================================================================
