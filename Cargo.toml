[package]
name = "rru_calculator"
version = "0.1.0"
edition = "2021"

[dependencies]
csv = "1.3"
serde = { version = "1.0", features = ["derive"] }
tokio = { version = "1.0", features = ["full"] }
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "fmt", "local-time"] }
colored = "2.0"
rust_xlsxwriter = "0.79"
walkdir = "2.4"
encoding_rs = "0.8"
encoding_rs_io = "0.1"
anyhow = "1.0"
thiserror = "1.0"
indexmap = "2.0"
regex = "1.10"
chrono = { version = "0.4", features = ["serde", "clock"] }
indicatif = "0.17"

[build-dependencies]
winres = "0.1"
