{"rustc": 16591470773350601817, "features": "[\"alloc\", \"ansi\", \"default\", \"env-filter\", \"fmt\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"regex\", \"registry\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 4817557058868189149, "profile": 11202463608144111571, "path": 7997839937313752072, "deps": [[1009387600818341822, "matchers", false, 12984773729265285301], [1017461770342116999, "sharded_slab", false, 3609897429745633610], [3722963349756955755, "once_cell", false, 14387579438816930552], [6048213226671835012, "smallvec", false, 13815146424614708107], [8606274917505247608, "tracing", false, 14199415030228859663], [8614575489689151157, "nu_ansi_term", false, 13925090433371386484], [9451456094439810778, "regex", false, 12631618064487690839], [10806489435541507125, "tracing_log", false, 2496055242903694696], [11033263105862272874, "tracing_core", false, 12227131581886285909], [12409575957772518135, "time", false, 17185374156186963832], [12427285511609802057, "thread_local", false, 8620580639892129515]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tracing-subscriber-5f3d5ab2c6f95605\\dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}