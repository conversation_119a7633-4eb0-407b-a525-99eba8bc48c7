#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RRU计算器图标生成器
将SVG图标转换为ICO格式，支持多种尺寸
"""

import os
import sys
from pathlib import Path

try:
    from PIL import Image, ImageDraw, ImageFont
    PIL_AVAILABLE = True
except ImportError:
    print("缺少必需的库，请安装：")
    print("pip install Pillow")
    PIL_AVAILABLE = False

try:
    import cairosvg
    CAIRO_AVAILABLE = True
except ImportError:
    CAIRO_AVAILABLE = False

def create_fallback_icon():
    """创建备用图标（如果SVG转换失败）"""
    print("创建备用图标...")

    # 创建多个尺寸的图标
    sizes = [16, 24, 32, 48, 64, 128, 256]
    images = []

    for size in sizes:
        # 创建图像
        img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)

        # 背景渐变效果（简化版）
        for y in range(size):
            alpha = int(255 * (1 - y / size * 0.3))
            color = (30, 58, 138, alpha)  # 深蓝色
            draw.line([(0, y), (size, y)], fill=color)

        # 绘制圆角矩形背景
        margin = size // 8
        draw.rounded_rectangle(
            [margin, margin, size - margin, size - margin],
            radius=size // 8,
            fill=(59, 130, 246, 255),  # 蓝色
            outline=(255, 255, 255, 128),
            width=max(1, size // 64)
        )

        # 绘制RRU设备
        center_x, center_y = size // 2, size // 2
        device_w, device_h = size // 3, size // 4

        # 设备主体
        draw.rounded_rectangle(
            [center_x - device_w//2, center_y - device_h//2,
             center_x + device_w//2, center_y + device_h//2],
            radius=size // 32,
            fill=(255, 255, 255, 255),
            outline=(226, 232, 240, 255)
        )

        # LED指示灯
        if size >= 32:
            led_size = max(1, size // 32)
            led_y = center_y - device_h//4

            # 绿色LED
            draw.ellipse([center_x - device_w//3 - led_size, led_y - led_size,
                         center_x - device_w//3 + led_size, led_y + led_size],
                        fill=(16, 185, 129, 255))

            # 蓝色LED
            draw.ellipse([center_x - led_size, led_y - led_size,
                         center_x + led_size, led_y + led_size],
                        fill=(59, 130, 246, 255))

            # 橙色LED
            draw.ellipse([center_x + device_w//3 - led_size, led_y - led_size,
                         center_x + device_w//3 + led_size, led_y + led_size],
                        fill=(245, 158, 11, 255))

        # 信号波纹
        if size >= 24:
            wave_y = center_y - device_h
            wave_width = max(1, size // 64)

            for i in range(3):
                radius = (i + 1) * size // 12
                alpha = 255 - i * 60
                draw.arc([center_x - radius, wave_y - radius//2,
                         center_x + radius, wave_y + radius//2],
                        start=-45, end=45,
                        fill=(52, 211, 153, alpha),
                        width=wave_width)

        # 添加文字（仅在较大尺寸时）
        if size >= 48:
            try:
                # 尝试使用系统字体
                font_size = max(8, size // 16)
                font = ImageFont.truetype("arial.ttf", font_size)
            except:
                font = ImageFont.load_default()

            text = "RRU"
            bbox = draw.textbbox((0, 0), text, font=font)
            text_w = bbox[2] - bbox[0]
            text_h = bbox[3] - bbox[1]

            draw.text((center_x - text_w//2, center_y + device_h//2 + size//16),
                     text, fill=(255, 255, 255, 255), font=font)

        images.append(img)

    return images

def svg_to_ico():
    """将SVG转换为ICO格式"""
    svg_path = Path("assets/icon.svg")
    ico_path = Path("assets/icon.ico")

    if not svg_path.exists():
        print(f"SVG文件不存在: {svg_path}")
        return False

    try:
        print("正在转换SVG到ICO格式...")

        # 定义需要的图标尺寸
        sizes = [16, 24, 32, 48, 64, 128, 256]
        images = []

        for size in sizes:
            print(f"  生成 {size}x{size} 图标...")

            # 将SVG转换为PNG字节
            png_data = cairosvg.svg2png(
                url=str(svg_path),
                output_width=size,
                output_height=size
            )

            # 从字节数据创建PIL图像
            img = Image.open(io.BytesIO(png_data))

            # 确保是RGBA模式
            if img.mode != 'RGBA':
                img = img.convert('RGBA')

            images.append(img)

        # 保存为ICO文件
        print(f"保存ICO文件: {ico_path}")
        images[0].save(
            ico_path,
            format='ICO',
            sizes=[(img.width, img.height) for img in images],
            append_images=images[1:]
        )

        print("✅ SVG转换为ICO成功！")
        return True

    except Exception as e:
        print(f"❌ SVG转换失败: {e}")
        return False

def main():
    """主函数"""
    print("🎨 RRU计算器图标生成器")
    print("=" * 50)

    if not PIL_AVAILABLE:
        print("❌ 缺少PIL库，无法生成图标")
        print("请运行: pip install Pillow")
        return 1

    # 确保assets目录存在
    assets_dir = Path("assets")
    assets_dir.mkdir(exist_ok=True)

    # 尝试SVG转换
    success = False

    if CAIRO_AVAILABLE:
        try:
            import io
            success = svg_to_ico()
        except Exception as e:
            print(f"SVG转换失败: {e}")
    else:
        print("cairosvg库未安装，将创建备用图标")

    # 如果SVG转换失败，创建备用图标
    if not success:
        print("创建备用图标...")
        images = create_fallback_icon()

        ico_path = Path("assets/icon.ico")
        images[0].save(
            ico_path,
            format='ICO',
            sizes=[(img.width, img.height) for img in images],
            append_images=images[1:]
        )

        print(f"✅ 备用图标创建成功: {ico_path}")

    # 验证文件
    ico_path = Path("assets/icon.ico")
    if ico_path.exists():
        size = ico_path.stat().st_size
        print(f"📁 图标文件大小: {size:,} 字节")
        print("🎯 图标已准备就绪，可以编译程序了！")
    else:
        print("❌ 图标文件创建失败")
        return 1

    return 0

if __name__ == "__main__":
    sys.exit(main())
