#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Excel文件列结构的脚本
"""

import pandas as pd
import sys

def test_excel_columns(filename):
    """测试Excel文件的列结构"""
    try:
        # 读取Excel文件的前几行来查看列结构
        df = pd.read_excel(filename, nrows=5)
        
        print(f"Excel文件: {filename}")
        print(f"总列数: {len(df.columns)}")
        print("\n列名列表:")
        for i, col in enumerate(df.columns, 1):
            print(f"{i:2d}. {col}")
        
        print(f"\n数据行数: {len(df)}")
        
        # 检查是否包含合并后的列
        merged_columns = [
            "小区级射频单元(RRU)数量",
            "小区级射频单元信息序列", 
            "小区级RRU型号数量",
            "小区级RRU型号"
        ]
        
        print("\n合并列检查:")
        for col in merged_columns:
            if col in df.columns:
                print(f"✓ {col} - 存在")
            else:
                print(f"✗ {col} - 不存在")
        
        # 检查是否还有旧的分离列
        old_columns = [
            "4G小区级射频单元(RRU)数量",
            "5G小区级射频单元(RRU)数量",
            "4G小区级射频单元信息序列",
            "5G小区级射频单元信息序列",
            "4G小区级RRU型号数量",
            "5G小区级RRU型号数量",
            "4G小区级RRU型号(去重)",
            "5G小区级RRU型号(去重)"
        ]
        
        print("\n旧列检查:")
        for col in old_columns:
            if col in df.columns:
                print(f"✗ {col} - 仍然存在（应该已被移除）")
            else:
                print(f"✓ {col} - 已移除")
        
        # 显示前几行数据的合并列内容
        print("\n合并列数据示例:")
        for col in merged_columns:
            if col in df.columns:
                print(f"\n{col}:")
                for i, value in enumerate(df[col].head(3)):
                    print(f"  行{i+1}: {value}")
        
    except Exception as e:
        print(f"读取Excel文件时出错: {e}")

if __name__ == "__main__":
    filename = "RRU计算结果_20250530_092425.xlsx"
    test_excel_columns(filename)
