#[cfg(windows)]
extern crate winres;

#[cfg(windows)]
fn main() {
    let mut res = winres::WindowsResource::new();
    res.set_icon("assets/icon.ico")
        .set("ProductName", "RRU计算器")
        .set("FileDescription", "射频单元数据处理与分析工具")
        .set("CompanyName", "RRU Analytics")
        .set("LegalCopyright", "Copyright © 2025")
        .set("ProductVersion", "1.0.0")
        .set("FileVersion", "1.0.0");
    
    if let Err(e) = res.compile() {
        eprintln!("Warning: Failed to compile Windows resources: {}", e);
    }
}

#[cfg(not(windows))]
fn main() {
    // 非Windows平台不需要图标
}
