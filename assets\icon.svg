<?xml version="1.0" encoding="UTF-8"?>
<svg width="256" height="256" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景渐变 -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#60a5fa;stop-opacity:1" />
    </linearGradient>
    
    <!-- 信号波纹渐变 -->
    <linearGradient id="waveGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:0.8" />
      <stop offset="50%" style="stop-color:#34d399;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#6ee7b7;stop-opacity:1" />
    </linearGradient>
    
    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="4" stdDeviation="3" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
    
    <!-- 内发光效果 -->
    <filter id="glow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- 主背景圆角矩形 -->
  <rect x="16" y="16" width="224" height="224" rx="32" ry="32" 
        fill="url(#bgGradient)" filter="url(#shadow)"/>
  
  <!-- 内部装饰边框 -->
  <rect x="24" y="24" width="208" height="208" rx="24" ry="24" 
        fill="none" stroke="#ffffff" stroke-width="1" opacity="0.2"/>
  
  <!-- 中央RRU设备图标 -->
  <g transform="translate(128, 128)">
    <!-- 主设备机箱 -->
    <rect x="-48" y="-32" width="96" height="64" rx="8" ry="8" 
          fill="#ffffff" filter="url(#shadow)"/>
    
    <!-- 设备面板 -->
    <rect x="-44" y="-28" width="88" height="56" rx="4" ry="4" 
          fill="#f1f5f9" stroke="#e2e8f0" stroke-width="1"/>
    
    <!-- LED指示灯 -->
    <circle cx="-32" cy="-16" r="3" fill="#10b981" filter="url(#glow)"/>
    <circle cx="-20" cy="-16" r="3" fill="#3b82f6" filter="url(#glow)"/>
    <circle cx="-8" cy="-16" r="3" fill="#f59e0b" filter="url(#glow)"/>
    
    <!-- 天线连接器 -->
    <rect x="20" y="-20" width="16" height="8" rx="2" ry="2" fill="#6b7280"/>
    <rect x="20" y="-8" width="16" height="8" rx="2" ry="2" fill="#6b7280"/>
    <rect x="20" y="4" width="16" height="8" rx="2" ry="2" fill="#6b7280"/>
    <rect x="20" y="16" width="16" height="8" rx="2" ry="2" fill="#6b7280"/>
    
    <!-- 散热孔 -->
    <g opacity="0.6">
      <line x1="-36" y1="8" x2="-36" y2="20" stroke="#9ca3af" stroke-width="1"/>
      <line x1="-32" y1="8" x2="-32" y2="20" stroke="#9ca3af" stroke-width="1"/>
      <line x1="-28" y1="8" x2="-28" y2="20" stroke="#9ca3af" stroke-width="1"/>
      <line x1="-24" y1="8" x2="-24" y2="20" stroke="#9ca3af" stroke-width="1"/>
      <line x1="-20" y1="8" x2="-20" y2="20" stroke="#9ca3af" stroke-width="1"/>
    </g>
  </g>
  
  <!-- 信号波纹效果 -->
  <g transform="translate(128, 80)" opacity="0.8">
    <!-- 第一层波纹 -->
    <path d="M -20 0 Q 0 -15 20 0 Q 0 15 -20 0" 
          fill="none" stroke="url(#waveGradient)" stroke-width="2" filter="url(#glow)"/>
    <!-- 第二层波纹 -->
    <path d="M -30 0 Q 0 -25 30 0 Q 0 25 -30 0" 
          fill="none" stroke="url(#waveGradient)" stroke-width="1.5" opacity="0.7"/>
    <!-- 第三层波纹 -->
    <path d="M -40 0 Q 0 -35 40 0 Q 0 35 -40 0" 
          fill="none" stroke="url(#waveGradient)" stroke-width="1" opacity="0.5"/>
  </g>
  
  <!-- 数据流动效果 -->
  <g transform="translate(128, 180)" opacity="0.6">
    <!-- 数据点 -->
    <circle cx="-24" cy="0" r="2" fill="#34d399">
      <animate attributeName="opacity" values="0.3;1;0.3" dur="2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="-12" cy="0" r="2" fill="#34d399">
      <animate attributeName="opacity" values="0.3;1;0.3" dur="2s" begin="0.3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="0" cy="0" r="2" fill="#34d399">
      <animate attributeName="opacity" values="0.3;1;0.3" dur="2s" begin="0.6s" repeatCount="indefinite"/>
    </circle>
    <circle cx="12" cy="0" r="2" fill="#34d399">
      <animate attributeName="opacity" values="0.3;1;0.3" dur="2s" begin="0.9s" repeatCount="indefinite"/>
    </circle>
    <circle cx="24" cy="0" r="2" fill="#34d399">
      <animate attributeName="opacity" values="0.3;1;0.3" dur="2s" begin="1.2s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- 底部标识 -->
  <g transform="translate(128, 220)">
    <text x="0" y="0" text-anchor="middle" font-family="Arial, sans-serif" 
          font-size="14" font-weight="bold" fill="#ffffff" opacity="0.9">RRU</text>
    <text x="0" y="16" text-anchor="middle" font-family="Arial, sans-serif" 
          font-size="10" fill="#ffffff" opacity="0.7">CALC</text>
  </g>
  
  <!-- 角落装饰元素 -->
  <g opacity="0.3">
    <!-- 左上角 -->
    <path d="M 32 48 L 48 32 L 48 48 Z" fill="#ffffff"/>
    <!-- 右下角 -->
    <path d="M 208 224 L 224 208 L 208 208 Z" fill="#ffffff"/>
  </g>
</svg>
