{"rustc": 16591470773350601817, "features": "[\"with-alloc\"]", "declared_features": "[\"alloc\", \"block-boundary\", \"compiler_builtins\", \"core\", \"default\", \"rustc-dep-of-std\", \"serde\", \"simd\", \"simd-adler32\", \"std\", \"with-alloc\"]", "target": 8661567070972402511, "profile": 11250625435679592442, "path": 6459759996190113567, "deps": [[15407850927583745935, "adler2", false, 6357540203388242327]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\miniz_oxide-47db39b41f6d5126\\dep-lib-miniz_oxide", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}