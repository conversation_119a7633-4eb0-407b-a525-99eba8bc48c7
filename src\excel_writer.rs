use crate::error::Result;
use colored::*;
use indexmap::IndexMap;
use rust_xlsxwriter::{Color, Format, Workbook, Worksheet};
use tracing::info;
use indicatif::{ProgressBar, ProgressStyle};
use std::time::Instant;

pub struct ExcelWriter;

impl ExcelWriter {
    pub fn new() -> Self {
        Self
    }

    pub async fn write_excel(
        &self,
        data: &[IndexMap<String, String>],
        filename: &str,
    ) -> Result<()> {
        let start_time = Instant::now();
        info!("{} {}", "▶ 生成Excel报告".blue().bold(), filename.bright_green().bold());

        let mut workbook = Workbook::new();
        let mut worksheet = workbook.add_worksheet();

        // 设置工作表名称
        worksheet.set_name("RRU Analysis Report")?;

        // 冻结首行
        worksheet.set_freeze_panes(1, 0)?;

        // 设置表头格式 - 深蓝色主题
        let header_format = Format::new()
            .set_background_color(Color::RGB(0x2E4A6B)) // 深蓝色
            .set_font_color(Color::White)
            .set_bold()
            .set_border(rust_xlsxwriter::FormatBorder::Medium)
            .set_border_color(Color::RGB(0x1C3A57))
            .set_align(rust_xlsxwriter::FormatAlign::Center)
            .set_font_size(11);

        // 设置数据格式 - 交替行颜色
        let data_format_even = Format::new()
            .set_background_color(Color::RGB(0xF8F9FA)) // 浅灰色
            .set_border(rust_xlsxwriter::FormatBorder::Thin)
            .set_border_color(Color::RGB(0xDEE2E6))
            .set_font_size(10);

        let data_format_odd = Format::new()
            .set_background_color(Color::White)
            .set_border(rust_xlsxwriter::FormatBorder::Thin)
            .set_border_color(Color::RGB(0xDEE2E6))
            .set_font_size(10);

        // 数值格式
        let number_format = Format::new()
            .set_border(rust_xlsxwriter::FormatBorder::Thin)
            .set_border_color(Color::RGB(0xDEE2E6))
            .set_align(rust_xlsxwriter::FormatAlign::Right)
            .set_font_size(10);

        if data.is_empty() {
            info!("  └─ 无数据需要写入");
            return Ok(());
        }

        // 获取列标题
        let headers: Vec<&String> = data[0].keys().collect();

        // 创建进度条
        let total_operations = data.len() + headers.len() + 2; // 数据行 + 表头 + 保存 + 列宽调整
        let pb = ProgressBar::new(total_operations as u64);
        pb.set_style(
            ProgressStyle::default_bar()
                .template("  ├─ [{bar:40.cyan/blue}] {pos:>7}/{len:7} ({percent:>3}%) {msg} [{elapsed_precise}]")
                .unwrap()
                .progress_chars("█▉▊▋▌▍▎▏ ")
        );
        pb.set_message("写入表头...");

        // 写入表头
        for (col_idx, header) in headers.iter().enumerate() {
            worksheet.write_string_with_format(0, col_idx as u16, *header, &header_format)?;
            pb.inc(1);
        }
        pb.set_message("写入数据...");

        // 写入数据 - 使用交替行颜色和智能格式化
        for (row_idx, row_data) in data.iter().enumerate() {
            let is_even_row = row_idx % 2 == 0;

            for (col_idx, header) in headers.iter().enumerate() {
                let empty_string = String::new();
                let value = row_data.get(*header).unwrap_or(&empty_string);

                // 选择格式：数值列使用右对齐，其他使用交替行颜色
                let format = if Self::is_numeric_column(header) {
                    &number_format
                } else if is_even_row {
                    &data_format_even
                } else {
                    &data_format_odd
                };

                worksheet.write_string_with_format(
                    (row_idx + 1) as u32,
                    col_idx as u16,
                    value,
                    format,
                )?;
            }

            // 更新进度条
            pb.inc(1);

            // 每1000行更新一次消息
            if (row_idx + 1) % 1000 == 0 {
                pb.set_message("写入数据中...");
            }
        }

        // 自动调整列宽
        pb.set_message("调整列宽...");
        self.auto_fit_columns(&mut worksheet, &headers, data)?;
        pb.inc(1);

        // 保存文件
        pb.set_message("正在保存Excel文件...");

        // 记录保存开始时间
        let save_start = std::time::Instant::now();

        // 实际保存文件
        workbook.save(filename)?;
        let save_elapsed = save_start.elapsed();

        pb.inc(1);

        // 显示保存完成信息
        pb.set_message("Excel文件保存完成");

        // 短暂停留显示保存完成信息，让用户看到保存完成状态
        std::thread::sleep(std::time::Duration::from_millis(500));

        // 完成进度条
        pb.finish_with_message("Excel报告生成完成");

        let elapsed = start_time.elapsed();
        info!("  └─ {} │ {} 行 │ {} 列 │ 总耗时 {:.2}秒 │ 保存耗时 {:.1}秒",
              "Excel报告生成完成".green().bold(),
              data.len().to_string().yellow().bold(),
              headers.len().to_string().yellow().bold(),
              elapsed.as_secs_f64().to_string().cyan().bold(),
              save_elapsed.as_secs_f64().to_string().cyan().bold());

        Ok(())
    }

    fn is_numeric_column(header: &str) -> bool {
        header.contains("数量") ||
        header.contains("count") ||
        header.contains("COUNT") ||
        header.contains("绑定小区数量") ||
        header.contains("RRU数量") ||
        header.contains("型号数量") ||
        header.contains("BBU数量") ||
        header.contains("小区级射频单元(RRU)数量") ||
        header.contains("小区级RRU型号数量")
    }

    fn auto_fit_columns(
        &self,
        worksheet: &mut Worksheet,
        headers: &[&String],
        data: &[IndexMap<String, String>],
    ) -> Result<()> {
        for (col_idx, header) in headers.iter().enumerate() {
            let mut max_length = header.chars().count();

            // 检查数据中的最大长度
            for row in data {
                if let Some(value) = row.get(*header) {
                    let length = value.chars().count();
                    if length > max_length {
                        max_length = length;
                    }
                }
            }

            // 设置列宽，最大不超过50个字符
            let width = (max_length + 2).min(50) as f64;
            worksheet.set_column_width(col_idx as u16, width)?;
        }

        Ok(())
    }
}
