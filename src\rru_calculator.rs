use crate::data_reader::DataReader;
use crate::excel_writer::ExcelWriter;
use crate::error::{Result, RruError};
use crate::types::{StockData, LteParam, NrParam, StationStatsMap};
use colored::*;
use indexmap::IndexMap;
use std::collections::HashMap;
use tracing::{info, warn};

pub struct RruCalculator {
    stock_path: String,
    param_path: String,
}

impl RruCalculator {
    pub async fn new() -> Result<Self> {
        let current_dir = std::env::current_dir()?;

        let stock_path = current_dir.join("45G存量数据");
        let param_path = current_dir.join("工参数据");

        // 确保目录存在
        std::fs::create_dir_all(&stock_path)?;
        std::fs::create_dir_all(&param_path)?;

        Ok(Self {
            stock_path: stock_path.to_string_lossy().to_string(),
            param_path: param_path.to_string_lossy().to_string(),
        })
    }

    pub async fn process_data(&mut self) -> Result<()> {
        info!("{}", "▶ 启动数据处理流水线".cyan().bold());

        // 读取存量板数据
        let stock_data = DataReader::read_stock_files(&self.stock_path).await?;

        // 读取4G工参
        let lte_data = DataReader::read_lte_param(&self.param_path).await?;

        // 读取5G工参
        let nr_data = DataReader::read_nr_param(&self.param_path).await?;

        // 数据验证
        if lte_data.is_none() && nr_data.is_none() {
            return Err(RruError::ParamFileNotFound);
        }

        // 处理统计数据
        let station_stats = self.calculate_station_stats(lte_data, nr_data).await?;

        // 合并数据
        let final_result = self.merge_data(stock_data, station_stats).await?;

        // 生成Excel报告
        let excel_writer = ExcelWriter::new();
        let output_filename = Self::generate_output_filename();
        excel_writer.write_excel(&final_result, &output_filename).await?;

        info!("{} {}", "▶ 处理完成，结果已保存至".green().bold(), output_filename.bright_green().bold());

        Ok(())
    }

    fn generate_output_filename() -> String {
        use chrono::Local;
        let now = Local::now();
        format!("RRU计算结果_{}.xlsx", now.format("%Y%m%d_%H%M%S"))
    }

    async fn calculate_station_stats(
        &self,
        lte_data: Option<Vec<LteParam>>,
        nr_data: Option<Vec<NrParam>>,
    ) -> Result<StationStatsMap> {
        info!("{}", "▶ 计算站点统计数据".blue().bold());

        let mut station_stats: StationStatsMap = HashMap::new();

        // 处理4G数据
        if let Some(lte_data) = lte_data {
            info!("  ├─ 正在处理4G数据统计...");
            self.process_lte_stats(&lte_data, &mut station_stats).await?;
        } else {
            warn!("  ├─ 未找到4G工参，仅计算5G小区级RRU");
        }

        // 处理5G数据
        if let Some(nr_data) = nr_data {
            info!("  ├─ 正在处理5G数据统计...");
            self.process_nr_stats(&nr_data, &mut station_stats).await?;
        } else {
            warn!("  ├─ 未找到5G工参，仅计算4G小区级RRU");
        }

        // 合并相同属性的列
        self.merge_common_fields(&mut station_stats).await?;

        info!("  └─ {} │ {} 个站点", "站点统计完成".green().bold(), station_stats.len().to_string().yellow().bold());

        Ok(station_stats)
    }

    async fn process_lte_stats(
        &self,
        lte_data: &[LteParam],
        station_stats: &mut StationStatsMap,
    ) -> Result<()> {
        let mut station_groups: HashMap<String, Vec<&LteParam>> = HashMap::new();

        // 按站名分组
        for record in lte_data {
            station_groups
                .entry(record.station_name.clone())
                .or_default()
                .push(record);
        }

        for (station_name, records) in station_groups {
            let stats = station_stats.entry(station_name.clone()).or_default();
            stats.network_element_name = station_name;

            // 计算4G统计信息
            stats.lte_cell_count = records.len() as u32;
            stats.lte_bound_cells = records
                .iter()
                .map(|r| r.ecgi.clone())
                .collect::<std::collections::HashSet<_>>()
                .into_iter()
                .collect::<Vec<_>>()
                .join("；");

            stats.lte_station_id = records
                .iter()
                .map(|r| r.station_id.clone())
                .collect::<std::collections::HashSet<_>>()
                .into_iter()
                .collect::<Vec<_>>()
                .join("；");

            stats.lte_downlink_bandwidth = records
                .iter()
                .map(|r| r.downlink_bandwidth.clone())
                .collect::<std::collections::HashSet<_>>()
                .into_iter()
                .collect::<Vec<_>>()
                .join("；");

            stats.lte_cell_rru_count = records
                .iter()
                .map(|r| r.cell_rru_count.parse::<u32>().unwrap_or(0))
                .sum();

            stats.lte_cell_rru_info_sequence = records
                .iter()
                .map(|r| r.cell_rru_info_sequence.clone())
                .collect::<std::collections::HashSet<_>>()
                .into_iter()
                .collect::<Vec<_>>()
                .join("；");

            stats.lte_cell_rru_model_count = records
                .iter()
                .map(|r| r.cell_rru_model_count.parse::<u32>().unwrap_or(0))
                .sum();

            stats.lte_cell_rru_models_unique = records
                .iter()
                .map(|r| r.cell_rru_models_unique.clone())
                .collect::<std::collections::HashSet<_>>()
                .into_iter()
                .collect::<Vec<_>>()
                .join("；");

            // 基站级信息取第一条记录
            if let Some(first_record) = records.first() {
                stats.lte_base_station_bbu_count = first_record.base_station_bbu_count.clone();
                stats.lte_base_station_bbu_info = first_record.base_station_bbu_info.clone();
                stats.lte_base_station_bbu_model_count = first_record.base_station_bbu_model_count.clone();
                stats.lte_base_station_service_bbu_model = first_record.base_station_service_bbu_model.clone();
                stats.lte_network_access_time = first_record.network_access_time.clone();
            }
        }

        Ok(())
    }

    async fn process_nr_stats(
        &self,
        nr_data: &[NrParam],
        station_stats: &mut StationStatsMap,
    ) -> Result<()> {
        let mut station_groups: HashMap<String, Vec<&NrParam>> = HashMap::new();

        // 按站名分组
        for record in nr_data {
            station_groups
                .entry(record.name.clone())
                .or_default()
                .push(record);
        }

        for (station_name, records) in station_groups {
            let stats = station_stats.entry(station_name.clone()).or_default();
            stats.network_element_name = station_name;

            // 计算5G统计信息
            stats.nr_cell_count = records.len() as u32;
            stats.nr_bound_cells = records
                .iter()
                .map(|r| r.ecgi.clone())
                .collect::<std::collections::HashSet<_>>()
                .into_iter()
                .collect::<Vec<_>>()
                .join("；");

            stats.nr_station_id = records
                .iter()
                .map(|r| r.station_id.clone())
                .collect::<std::collections::HashSet<_>>()
                .into_iter()
                .collect::<Vec<_>>()
                .join("；");

            stats.nr_downlink_bandwidth = records
                .iter()
                .map(|r| r.downlink_bandwidth.clone())
                .collect::<std::collections::HashSet<_>>()
                .into_iter()
                .collect::<Vec<_>>()
                .join("；");

            // 检查是否开通100M/60M
            stats.nr_100m_60m_enabled = if records.iter().any(|r| {
                r.downlink_bandwidth.contains("100") || r.downlink_bandwidth.contains("60")
            }) {
                "是".to_string()
            } else {
                "否".to_string()
            };

            stats.nr_cell_rru_count = records
                .iter()
                .map(|r| r.cell_rru_count.parse::<u32>().unwrap_or(0))
                .sum();

            stats.nr_cell_rru_info_sequence = records
                .iter()
                .map(|r| r.cell_rru_info_sequence.clone())
                .collect::<std::collections::HashSet<_>>()
                .into_iter()
                .collect::<Vec<_>>()
                .join("；");

            stats.nr_cell_rru_model_count = records
                .iter()
                .map(|r| r.cell_rru_model_count.parse::<u32>().unwrap_or(0))
                .sum();

            stats.nr_cell_rru_models_unique = records
                .iter()
                .map(|r| r.cell_rru_model_sequence.clone())
                .collect::<std::collections::HashSet<_>>()
                .into_iter()
                .collect::<Vec<_>>()
                .join("；");

            // 基站级信息取第一条记录
            if let Some(first_record) = records.first() {
                stats.nr_base_station_bbu_count = first_record.base_station_bbu_count.clone();
                stats.nr_base_station_bbu_info = first_record.base_station_bbu_info.clone();
                stats.nr_base_station_bbu_model_count = first_record.base_station_bbu_model_count.clone();
                stats.nr_base_station_service_bbu_model = first_record.base_station_service_bbu_model.clone();
                stats.nr_network_access_time = first_record.network_access_time.clone();
            }
        }

        Ok(())
    }

    async fn merge_common_fields(&self, station_stats: &mut StationStatsMap) -> Result<()> {
        for stats in station_stats.values_mut() {
            // 合并站号
            let mut station_ids = Vec::new();
            if !stats.lte_station_id.is_empty() {
                station_ids.push(stats.lte_station_id.clone());
            }
            if !stats.nr_station_id.is_empty() {
                station_ids.push(stats.nr_station_id.clone());
            }
            stats.combined_station_id = station_ids.join("；");

            // 合并下行带宽
            let mut bandwidths = Vec::new();
            if !stats.lte_downlink_bandwidth.is_empty() {
                bandwidths.push(stats.lte_downlink_bandwidth.clone());
            }
            if !stats.nr_downlink_bandwidth.is_empty() {
                bandwidths.push(stats.nr_downlink_bandwidth.clone());
            }
            stats.combined_downlink_bandwidth = bandwidths.join("；");

            // 合并入网时间
            let mut access_times = Vec::new();
            if !stats.lte_network_access_time.is_empty() {
                access_times.push(stats.lte_network_access_time.clone());
            }
            if !stats.nr_network_access_time.is_empty() {
                access_times.push(stats.nr_network_access_time.clone());
            }
            stats.combined_network_access_time = access_times.join("；");

            // 合并基站级基带板信息
            let mut bbu_counts = Vec::new();
            if !stats.lte_base_station_bbu_count.is_empty() {
                bbu_counts.push(stats.lte_base_station_bbu_count.clone());
            }
            if !stats.nr_base_station_bbu_count.is_empty() {
                bbu_counts.push(stats.nr_base_station_bbu_count.clone());
            }
            stats.combined_base_station_bbu_count = bbu_counts.join("；");

            let mut bbu_infos = Vec::new();
            if !stats.lte_base_station_bbu_info.is_empty() {
                bbu_infos.push(stats.lte_base_station_bbu_info.clone());
            }
            if !stats.nr_base_station_bbu_info.is_empty() {
                bbu_infos.push(stats.nr_base_station_bbu_info.clone());
            }
            stats.combined_base_station_bbu_info = bbu_infos.join("；");

            let mut bbu_model_counts = Vec::new();
            if !stats.lte_base_station_bbu_model_count.is_empty() {
                bbu_model_counts.push(stats.lte_base_station_bbu_model_count.clone());
            }
            if !stats.nr_base_station_bbu_model_count.is_empty() {
                bbu_model_counts.push(stats.nr_base_station_bbu_model_count.clone());
            }
            stats.combined_base_station_bbu_model_count = bbu_model_counts.join("；");

            let mut service_bbu_models = Vec::new();
            if !stats.lte_base_station_service_bbu_model.is_empty() {
                service_bbu_models.push(stats.lte_base_station_service_bbu_model.clone());
            }
            if !stats.nr_base_station_service_bbu_model.is_empty() {
                service_bbu_models.push(stats.nr_base_station_service_bbu_model.clone());
            }
            stats.combined_base_station_service_bbu_model = service_bbu_models.join("；");
        }

        Ok(())
    }

    async fn merge_data(
        &self,
        stock_data: Vec<StockData>,
        station_stats: StationStatsMap,
    ) -> Result<Vec<IndexMap<String, String>>> {
        info!("{}", "▶ 合并数据集".blue().bold());

        let mut result = Vec::new();

        for stock_record in stock_data {
            let mut row = IndexMap::new();

            // 基础存量板信息
            row.insert("网元名称".to_string(), stock_record.network_element_name.clone());
            row.insert("单板名称".to_string(), stock_record.board_name);
            row.insert("单板类型".to_string(), stock_record.board_type);
            row.insert("机框号".to_string(), stock_record.frame_number);
            row.insert("机柜号".to_string(), stock_record.cabinet_number);
            row.insert("资产序列号".to_string(), stock_record.asset_serial_number);
            row.insert("槽位号".to_string(), stock_record.slot_number);

            // 获取对应的统计信息
            if let Some(stats) = station_stats.get(&stock_record.network_element_name) {
                // 合并后的基础信息
                row.insert("站号".to_string(), stats.combined_station_id.clone());
                row.insert("下行带宽".to_string(), stats.combined_downlink_bandwidth.clone());
                row.insert("入网时间".to_string(), stats.combined_network_access_time.clone());

                // 基站级基带板信息
                row.insert("基站级基带板(BBU)数量".to_string(), stats.combined_base_station_bbu_count.clone());
                row.insert("基站级基带板信息".to_string(), stats.combined_base_station_bbu_info.clone());
                row.insert("基站级BBU型号数量".to_string(), stats.combined_base_station_bbu_model_count.clone());
                row.insert("基站级服务基带板BBU型号".to_string(), stats.combined_base_station_service_bbu_model.clone());

                // 合并4G和5G的RRU信息
                let combined_rru_count = stats.lte_cell_rru_count + stats.nr_cell_rru_count;
                let combined_rru_info = Self::combine_sequences(&stats.lte_cell_rru_info_sequence, &stats.nr_cell_rru_info_sequence);
                let combined_rru_model_count = stats.lte_cell_rru_model_count + stats.nr_cell_rru_model_count;
                let combined_rru_models = Self::combine_and_deduplicate_models(&stats.lte_cell_rru_models_unique, &stats.nr_cell_rru_models_unique);

                // 插入合并后的列
                row.insert("小区级射频单元(RRU)数量".to_string(), combined_rru_count.to_string());
                row.insert("小区级射频单元信息序列".to_string(), combined_rru_info);
                row.insert("小区级RRU型号数量".to_string(), combined_rru_model_count.to_string());
                row.insert("小区级RRU型号".to_string(), combined_rru_models);

                // 统计信息
                row.insert("5G绑定小区数量".to_string(), stats.nr_cell_count.to_string());
                row.insert("5G绑定小区(CGI)".to_string(), stats.nr_bound_cells.clone());
                row.insert("共4G射频单元(RRU)绑定小区数量".to_string(), stats.lte_cell_count.to_string());
                row.insert("共4G射频单元(RRU) 绑定小区".to_string(), stats.lte_bound_cells.clone());
                row.insert("100M_60M是否开通".to_string(), stats.nr_100m_60m_enabled.clone());
            } else {
                // 如果没有找到对应的统计信息，填充默认值
                row.insert("站号".to_string(), "".to_string());
                row.insert("下行带宽".to_string(), "".to_string());
                row.insert("入网时间".to_string(), "".to_string());
                row.insert("基站级基带板(BBU)数量".to_string(), "".to_string());
                row.insert("基站级基带板信息".to_string(), "".to_string());
                row.insert("基站级BBU型号数量".to_string(), "".to_string());
                row.insert("基站级服务基带板BBU型号".to_string(), "".to_string());
                row.insert("小区级射频单元(RRU)数量".to_string(), "0".to_string());
                row.insert("小区级射频单元信息序列".to_string(), "".to_string());
                row.insert("小区级RRU型号数量".to_string(), "0".to_string());
                row.insert("小区级RRU型号".to_string(), "".to_string());
                row.insert("5G绑定小区数量".to_string(), "0".to_string());
                row.insert("5G绑定小区(CGI)".to_string(), "".to_string());
                row.insert("共4G射频单元(RRU)绑定小区数量".to_string(), "0".to_string());
                row.insert("共4G射频单元(RRU) 绑定小区".to_string(), "".to_string());
                row.insert("100M_60M是否开通".to_string(), "否".to_string());
            }

            result.push(row);
        }

        // 打印统计信息
        self.print_statistics(&result, &station_stats).await?;

        info!("  └─ {} │ {} 条记录", "数据合并完成".green().bold(), result.len().to_string().yellow().bold());

        Ok(result)
    }

    async fn print_statistics(
        &self,
        result: &[IndexMap<String, String>],
        station_stats: &StationStatsMap,
    ) -> Result<()> {
        let total_4g_cells: u32 = station_stats.values().map(|s| s.lte_cell_count).sum();
        let total_5g_cells: u32 = station_stats.values().map(|s| s.nr_cell_count).sum();

        let mut has_100m_60m_count = 0;
        let mut no_100m_60m_count = 0;

        for stats in station_stats.values() {
            if stats.nr_100m_60m_enabled == "是" {
                has_100m_60m_count += 1;
            } else {
                no_100m_60m_count += 1;
            }
        }

        info!("{}", "");
        info!("{}", "╔══════════════════════════════════════════════════════════════════════════════╗".bright_cyan());
        info!("{}", "║                                数据处理汇总                                  ║".bright_cyan().bold());
        info!("{}", "╠══════════════════════════════════════════════════════════════════════════════╣".bright_cyan());
        info!("║ {} │ {} 个小区", "4G 小区总数     ".bright_blue().bold(), format!("{:>12}", total_4g_cells).yellow().bold());
        info!("║ {} │ {} 个小区", "5G 小区总数     ".bright_blue().bold(), format!("{:>12}", total_5g_cells).yellow().bold());
        info!("║ {} │ {} 个站点", "100M/60M 已开通 ".green().bold(), format!("{:>12}", has_100m_60m_count).green().bold());
        info!("║ {} │ {} 个站点", "100M/60M 未开通 ".red().bold(), format!("{:>12}", no_100m_60m_count).red().bold());
        info!("║ {} │ {} 条记录", "最终数据集      ".bright_magenta().bold(), format!("{:>12}", result.len()).yellow().bold());
        info!("{}", "╚══════════════════════════════════════════════════════════════════════════════╝".bright_cyan());

        Ok(())
    }

    /// 合并两个序列字符串，用分号分隔
    fn combine_sequences(seq1: &str, seq2: &str) -> String {
        let mut combined = Vec::new();

        if !seq1.is_empty() {
            combined.push(seq1);
        }
        if !seq2.is_empty() {
            combined.push(seq2);
        }

        combined.join("；")
    }

    /// 合并并去重两个型号字符串
    fn combine_and_deduplicate_models(models1: &str, models2: &str) -> String {
        use std::collections::HashSet;

        let mut all_models = HashSet::new();

        // 处理第一个型号字符串
        if !models1.is_empty() {
            for model in models1.split('；') {
                let trimmed = model.trim();
                if !trimmed.is_empty() {
                    all_models.insert(trimmed.to_string());
                }
            }
        }

        // 处理第二个型号字符串
        if !models2.is_empty() {
            for model in models2.split('；') {
                let trimmed = model.trim();
                if !trimmed.is_empty() {
                    all_models.insert(trimmed.to_string());
                }
            }
        }

        // 转换为排序的向量并用分号连接
        let mut sorted_models: Vec<String> = all_models.into_iter().collect();
        sorted_models.sort();
        sorted_models.join("；")
    }
}