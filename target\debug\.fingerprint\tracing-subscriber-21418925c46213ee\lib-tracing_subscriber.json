{"rustc": 16591470773350601817, "features": "[\"alloc\", \"ansi\", \"default\", \"env-filter\", \"fmt\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"regex\", \"registry\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"tracing\", \"tracing-log\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 4817557058868189149, "profile": 6355579909791343455, "path": 7997839937313752072, "deps": [[1009387600818341822, "matchers", false, 14936649480106181931], [1017461770342116999, "sharded_slab", false, 18042180730576422010], [3722963349756955755, "once_cell", false, 3471486782772647257], [6048213226671835012, "smallvec", false, 11153306391226440482], [8606274917505247608, "tracing", false, 4668080582840600800], [8614575489689151157, "nu_ansi_term", false, 11500568989729297338], [9451456094439810778, "regex", false, 14256382618042947820], [10806489435541507125, "tracing_log", false, 5214385121570960082], [11033263105862272874, "tracing_core", false, 5438101220295683711], [12427285511609802057, "thread_local", false, 8225184691292273809]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tracing-subscriber-21418925c46213ee\\dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}