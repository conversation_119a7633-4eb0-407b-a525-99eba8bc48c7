use colored::*;
use rru_calculator::{RruCalculator, Result};
use tracing::{info, error};
use tracing_subscriber::{fmt, EnvFilter};

#[tokio::main]
async fn main() -> Result<()> {
    // 初始化日志系统
    init_logger();

    // 打印欢迎信息
    print_welcome();

    // 创建RRU计算器实例
    let mut calculator = RruCalculator::new().await?;

    // 执行数据处理
    match calculator.process_data().await {
        Ok(_) => {
            info!("{}", ">>> 数据处理完成，程序执行成功 <<<".green().bold());
        }
        Err(e) => {
            error!("{}: {}", ">>> 数据处理失败 <<<".red().bold(), e);
            return Err(e);
        }
    }

    Ok(())
}

fn init_logger() {
    let filter = EnvFilter::try_from_default_env()
        .unwrap_or_else(|_| EnvFilter::new("info"));

    fmt()
        .with_env_filter(filter)
        .with_target(false)
        .with_thread_ids(false)
        .with_file(false)
        .with_line_number(false)
        .with_timer(fmt::time::LocalTime::rfc_3339())
        .init();
}

fn print_welcome() {
    println!("{}", "╔".repeat(80).bright_cyan());
    println!("{}", "║".bright_cyan().bold());
    println!("{}", format!("║{}║", " ".repeat(78)).bright_cyan().bold());
    println!("{}", format!("║{}     共RRU统计-小区级      {}║",
        " ".repeat(26), " ".repeat(25)).bright_cyan().bold());
    println!("{}", format!("║{} 射频单元数据处理与分析工具{}║",
        " ".repeat(26), " ".repeat(25)).bright_cyan().bold());
    println!("{}", format!("║{}        当前版本 1.0.0       {}║",
        " ".repeat(25), " ".repeat(24)).bright_cyan().bold());
    println!("{}", format!("║{}║", " ".repeat(78)).bright_cyan().bold());
    println!("{}", "║".bright_cyan().bold());
    println!("{}", "╚".repeat(80).bright_cyan());
    println!();
}
