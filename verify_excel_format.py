#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证Excel文件格式的脚本
"""

import pandas as pd
from openpyxl import load_workbook
import sys

def verify_excel_format(filename):
    """验证Excel文件的格式设置"""
    try:
        # 使用openpyxl读取Excel文件以检查格式
        wb = load_workbook(filename)
        ws = wb.active
        
        print(f"Excel文件: {filename}")
        print(f"工作表名称: {ws.title}")
        
        # 检查表头行高
        header_row_height = ws.row_dimensions[1].height
        print(f"表头行高: {header_row_height}")
        
        # 检查表头格式
        print("\n表头格式检查:")
        header_cell = ws['A1']
        print(f"表头单元格 A1: {header_cell.value}")
        print(f"  - 背景色: {header_cell.fill.start_color.rgb if header_cell.fill.start_color else '无'}")
        print(f"  - 字体颜色: {header_cell.font.color.rgb if header_cell.font.color else '无'}")
        print(f"  - 是否加粗: {header_cell.font.bold}")
        print(f"  - 对齐方式: {header_cell.alignment.horizontal}")
        print(f"  - 自动换行: {header_cell.alignment.wrap_text}")
        
        # 检查数据格式
        print("\n数据格式检查:")
        data_cell = ws['A2']
        if data_cell.value:
            print(f"数据单元格 A2: {data_cell.value}")
            print(f"  - 背景色: {data_cell.fill.start_color.rgb if data_cell.fill.start_color else '无'}")
            print(f"  - 对齐方式: {data_cell.alignment.horizontal}")
        
        # 检查列宽
        print("\n列宽检查 (前5列):")
        for i, col in enumerate(['A', 'B', 'C', 'D', 'E'], 1):
            col_width = ws.column_dimensions[col].width
            header_value = ws[f'{col}1'].value
            print(f"列 {col} ({header_value[:20] if header_value else 'N/A'}...): {col_width:.1f}")
        
        # 使用pandas读取数据检查内容
        df = pd.read_excel(filename, nrows=3)
        print(f"\n数据内容检查:")
        print(f"总列数: {len(df.columns)}")
        
        # 检查合并后的列
        merged_columns = [
            "小区级射频单元(RRU)数量",
            "小区级射频单元信息序列", 
            "小区级RRU型号数量",
            "小区级RRU型号"
        ]
        
        print("\n合并列存在性检查:")
        for col in merged_columns:
            exists = col in df.columns
            print(f"{'✓' if exists else '✗'} {col}")
            if exists and not df[col].empty:
                sample_value = df[col].iloc[0] if pd.notna(df[col].iloc[0]) else "空值"
                print(f"    示例值: {str(sample_value)[:50]}...")
        
        wb.close()
        
    except Exception as e:
        print(f"验证Excel文件时出错: {e}")

if __name__ == "__main__":
    filename = "RRU计算结果_20250530_092721.xlsx"
    verify_excel_format(filename)
